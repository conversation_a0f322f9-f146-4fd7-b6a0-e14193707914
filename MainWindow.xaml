<Window x:Class="FinancialTracker.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:FinancialTracker"
        Title="Financial Tracker"
        Height="800"
        Width="1400"
        WindowStartupLocation="CenterScreen"
        Background="{DynamicResource MaterialDesignPaper}">

    <Window.Resources>
        <local:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Top Bar -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="Finance" Width="32" Height="32" Margin="0,0,8,0"/>
                    <TextBlock Text="Financial Tracker" FontSize="20" VerticalAlignment="Center" Foreground="White"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button x:Name="DashboardButton" Content="Dashboard" Style="{StaticResource MaterialDesignFlatButton}"
                            Foreground="White" Margin="8,0" Click="DashboardButton_Click"/>
                </StackPanel>
            </Grid>
        </materialDesign:ColorZone>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1">
            <Grid Margin="16">
                <!-- Dashboard Content -->
                <Grid x:Name="DashboardContent" Visibility="Visible">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Header -->
                    <TextBlock Grid.Row="0" Text="Projects Overview" FontSize="20" FontWeight="Bold" Margin="0,0,0,12"/>

                    <!-- Quick Financial Overview - Ultra Compact -->
                    <materialDesign:Card Grid.Row="1" Margin="0,0,0,8" Background="#F8F9FA" Padding="8">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="FolderMultiple" Width="14" Height="14" Foreground="#1976D2" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                <TextBlock Text="Projects:" FontSize="12" Opacity="0.8" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBlock x:Name="TotalProjectsText" Text="0" FontSize="14" FontWeight="Bold" Foreground="#1976D2" VerticalAlignment="Center" Margin="2,0,0,0"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="CurrencyUsd" Width="14" Height="14" Foreground="#388E3C" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                <TextBlock Text="Total:" FontSize="12" Opacity="0.8" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBlock x:Name="TotalPOValueText" Text="$0" FontSize="14" FontWeight="Bold" Foreground="#388E3C" VerticalAlignment="Center" Margin="2,0,0,0"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="TrendingUp" Width="14" Height="14" Foreground="#F57C00" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                <TextBlock Text="Spent:" FontSize="12" Opacity="0.8" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBlock x:Name="TotalSpentText" Text="$0" FontSize="14" FontWeight="Bold" Foreground="#F57C00" VerticalAlignment="Center" Margin="2,0,0,0"/>
                            </StackPanel>

                            <StackPanel Grid.Column="3" Orientation="Horizontal" HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="TrendingDown" Width="14" Height="14" Foreground="#D32F2F" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                <TextBlock Text="Left:" FontSize="12" Opacity="0.8" VerticalAlignment="Center" FontWeight="Medium"/>
                                <TextBlock x:Name="TotalRemainingText" Text="$0" FontSize="14" FontWeight="Bold" Foreground="#D32F2F" VerticalAlignment="Center" Margin="2,0,0,0"/>
                            </StackPanel>
                        </Grid>
                    </materialDesign:Card>





                    <!-- Projects Details - Enhanced with maximum space -->
                    <materialDesign:Card Grid.Row="2" Margin="0" Padding="20" Background="#FFFFFF">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            <!-- Header - Enhanced with better spacing -->
                            <Grid Grid.Row="0" Margin="0,0,0,16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <StackPanel Grid.Column="0" VerticalAlignment="Center">
                                    <TextBlock Text="Financial Projects Summary" FontSize="22" FontWeight="Bold" Margin="0,0,0,6"/>
                                    <TextBlock Text="Complete overview of all project finances including PO, Tasks, and Services breakdown"
                                               FontSize="13" Opacity="0.7"/>
                                </StackPanel>
                                <Button Grid.Column="1" Style="{StaticResource MaterialDesignRaisedButton}"
                                        FontSize="13" Padding="16,8" Click="AddProjectButton_Click">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,6,0"/>
                                        <TextBlock Text="Add New Project"/>
                                    </StackPanel>
                                </Button>
                            </Grid>

                            <!-- Search and Filter Section - Compact -->
                            <materialDesign:Card Grid.Row="1" Margin="0,0,0,8" Padding="0">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- Header -->
                                    <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryLight" Padding="16,12">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                                                <materialDesign:PackIcon Kind="FilterVariant" Width="20" Height="20" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                                <TextBlock Text="Search and Filter" FontSize="14" FontWeight="Medium" VerticalAlignment="Center"/>
                                            </StackPanel>

                                            <StackPanel Grid.Column="2" Orientation="Horizontal">
                                                <Button x:Name="ClearFiltersButton"
                                                        Style="{StaticResource MaterialDesignOutlinedButton}"
                                                        Margin="0,0,8,0" Padding="12,6" FontSize="12"
                                                        Click="ClearFiltersButton_Click">
                                                    <StackPanel Orientation="Horizontal">
                                                        <materialDesign:PackIcon Kind="FilterRemove" Width="16" Height="16" Margin="0,0,4,0"/>
                                                        <TextBlock Text="Clear Filters"/>
                                                    </StackPanel>
                                                </Button>

                                                <Button x:Name="RefreshButton" Style="{StaticResource MaterialDesignIconButton}"
                                                        ToolTip="Refresh Data" Click="RefreshButton_Click">
                                                    <materialDesign:PackIcon Kind="Refresh" Width="18" Height="18"/>
                                                </Button>
                                            </StackPanel>
                                        </Grid>
                                    </materialDesign:ColorZone>

                                    <!-- Filter Controls - Better width utilization -->
                                    <Grid Grid.Row="1" Margin="16,12">
                                        <!-- Search Box with better spacing -->
                                        <StackPanel HorizontalAlignment="Center" MaxWidth="800">
                                            <TextBlock Text="Search Projects" FontSize="13" FontWeight="Medium" Margin="0,0,0,6" Opacity="0.8" HorizontalAlignment="Center"/>
                                            <Grid>
                                                <TextBox x:Name="SearchTextBox" materialDesign:HintAssist.Hint="Type project name or description to search..."
                                                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                                         Padding="45,12,12,12" MinWidth="600" FontSize="13"
                                                         TextChanged="SearchTextBox_TextChanged"/>
                                                <materialDesign:PackIcon Kind="Magnify" Width="22" Height="22"
                                                                       HorizontalAlignment="Left" VerticalAlignment="Center"
                                                                       Margin="15,0,0,0" Opacity="0.6" />
                                            </Grid>
                                        </StackPanel>
                                    </Grid>
                                </Grid>
                            </materialDesign:Card>
                            <!-- Projects Table - No horizontal scroll, all content visible -->
                            <DataGrid Grid.Row="2" x:Name="ProjectsSummaryDataGrid" AutoGenerateColumns="False" CanUserAddRows="False"
                                      IsReadOnly="True" SelectionMode="Single" GridLinesVisibility="All"
                                      HorizontalContentAlignment="Center" VerticalContentAlignment="Center"
                                      BorderBrush="#BDBDBD" BorderThickness="2"
                                      ColumnWidth="*" HorizontalScrollBarVisibility="Disabled" VerticalScrollBarVisibility="Auto"
                                      Background="#FAFAFA" AlternatingRowBackground="#F5F5F5"
                                      MinHeight="400" MaxHeight="600">
                                <DataGrid.Resources>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#2E3440"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="FontSize" Value="14"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="VerticalContentAlignment" Value="Center"/>
                                        <Setter Property="BorderBrush" Value="#434C5E"/>
                                        <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                        <Setter Property="Padding" Value="15,12"/>
                                        <Setter Property="Height" Value="50"/>
                                    </Style>
                                    <Style TargetType="DataGridCell">
                                        <Setter Property="BorderBrush" Value="#D8DEE9"/>
                                        <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                        <Setter Property="Padding" Value="12,10"/>
                                        <Setter Property="FontSize" Value="14"/>
                                        <Setter Property="Height" Value="55"/>
                                        <Setter Property="VerticalContentAlignment" Value="Center"/>
                                    </Style>
                                    <Style TargetType="DataGridRow">
                                        <Setter Property="Height" Value="55"/>
                                        <Setter Property="FontSize" Value="14"/>
                                    </Style>
                                </DataGrid.Resources>
                                <DataGrid.Columns>
                                    <!-- Project Name Column - Compact for 3-4 character names -->
                                    <DataGridTextColumn Header="Project Name" Binding="{Binding Name}" Width="0.8*" MinWidth="80">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="15"/>
                                                <Setter Property="Margin" Value="5,0"/>
                                                <Setter Property="Foreground" Value="#2E3440"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <!-- PO Section - Enhanced with better visibility -->
                                    <DataGridTextColumn Header="PO Total" Binding="{Binding POAmount, StringFormat='{}{0:C0}'}" Width="1.2*" MinWidth="140">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#1565C0"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="15"/>
                                                <Setter Property="Margin" Value="15,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="PO Spent" Binding="{Binding TotalSpent, StringFormat='{}{0:C0}'}" Width="1.2*" MinWidth="140">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#F57C00"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="15"/>
                                                <Setter Property="Margin" Value="15,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="PO Remaining" Binding="{Binding PORemaining, StringFormat='{}{0:C0}'}" Width="1.2*" MinWidth="140">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#D32F2F"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="15"/>
                                                <Setter Property="Margin" Value="15,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <!-- Tasks Section - Optimized for better visibility -->
                                    <DataGridTextColumn Header="Tasks Total" Binding="{Binding TaskCommitmentsAmount, StringFormat='{}{0:C0}'}" Width="1.1*" MinWidth="130">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#2E7D32"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="14"/>
                                                <Setter Property="Margin" Value="15,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="Tasks Spent" Binding="{Binding TasksSpent, StringFormat='{}{0:C0}'}" Width="1.1*" MinWidth="130">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#EF6C00"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="14"/>
                                                <Setter Property="Margin" Value="15,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="Tasks Remaining" Binding="{Binding TasksRemaining, StringFormat='{}{0:C0}'}" Width="1.1*" MinWidth="130">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#C2185B"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="14"/>
                                                <Setter Property="Margin" Value="15,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <!-- Services Section - Optimized for better visibility -->
                                    <DataGridTextColumn Header="Services Total" Binding="{Binding ServiceCommitmentsAmount, StringFormat='{}{0:C0}'}" Width="1.1*" MinWidth="130">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#7B1FA2"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="14"/>
                                                <Setter Property="Margin" Value="15,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="Services Spent" Binding="{Binding ServicesSpent, StringFormat='{}{0:C0}'}" Width="1.1*" MinWidth="130">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#D84315"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="14"/>
                                                <Setter Property="Margin" Value="15,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="Services Remaining" Binding="{Binding ServicesRemaining, StringFormat='{}{0:C0}'}" Width="1.1*" MinWidth="130">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Right"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#5D4037"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="14"/>
                                                <Setter Property="Margin" Value="15,0"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <!-- Status Column - Premium Design -->
                                    <DataGridTextColumn Header="Status" Binding="{Binding Status}" Width="1*" MinWidth="110">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                                <Setter Property="VerticalAlignment" Value="Center"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                                <Setter Property="FontSize" Value="14"/>
                                                <Setter Property="Margin" Value="15,0"/>
                                                <Setter Property="Foreground" Value="#1A237E"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>

                                    <!-- Actions Column - Compact buttons with full visibility -->
                                    <DataGridTemplateColumn Header="Actions" Width="2*" MinWidth="300">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="2">
                                                    <Button Content="Details" Style="{StaticResource MaterialDesignRaisedButton}"
                                                            Margin="2" Padding="10,6" FontSize="11" Click="ViewProjectDetailsButton_Click"
                                                            Background="#1976D2" BorderBrush="#1976D2"/>
                                                    <Button Content="Edit" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="2" Padding="10,6" FontSize="11" Click="EditProjectButton_Click"
                                                            BorderBrush="#FF9800" Foreground="#FF9800"/>
                                                    <Button Content="Delete" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="2" Padding="10,6" FontSize="11" Click="DeleteProjectButton_Click"
                                                            BorderBrush="#F44336" Foreground="#F44336"/>
                                                    <Button Content="Files" Style="{StaticResource MaterialDesignOutlinedButton}"
                                                            Margin="2" Padding="10,6" FontSize="11" Click="ViewProjectFilesButton_Click"
                                                            BorderBrush="#4CAF50" Foreground="#4CAF50"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Grid>
                    </materialDesign:Card>
                </Grid>




            </Grid>
        </ScrollViewer>

        <!-- Status Bar -->
        <materialDesign:ColorZone Grid.Row="2" Mode="PrimaryLight" Padding="16,4">
            <Grid>
                <TextBlock Text="Ready" VerticalAlignment="Center"/>
                <TextBlock Text="v1.0.0" HorizontalAlignment="Right" VerticalAlignment="Center"/>
            </Grid>
        </materialDesign:ColorZone>
    </Grid>
</Window>
