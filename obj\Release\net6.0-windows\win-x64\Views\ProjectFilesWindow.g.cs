﻿#pragma checksum "..\..\..\..\..\Views\ProjectFilesWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "96E93B3533FFCC369BDA88FA90ADAAB5B01327B2"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace FinancialTracker.Views {
    
    
    /// <summary>
    /// ProjectFilesWindow
    /// </summary>
    public partial class ProjectFilesWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 29 "..\..\..\..\..\Views\ProjectFilesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProjectNameText;
        
        #line default
        #line hidden
        
        
        #line 33 "..\..\..\..\..\Views\ProjectFilesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 37 "..\..\..\..\..\Views\ProjectFilesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddLetterButton;
        
        #line default
        #line hidden
        
        
        #line 39 "..\..\..\..\..\Views\ProjectFilesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddFileButton;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\..\..\Views\ProjectFilesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalFilesText;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\..\..\Views\ProjectFilesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CommitmentFilesText;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\..\Views\ProjectFilesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InvoiceFilesText;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\..\..\Views\ProjectFilesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LetterFilesText;
        
        #line default
        #line hidden
        
        
        #line 88 "..\..\..\..\..\Views\ProjectFilesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalSizeText;
        
        #line default
        #line hidden
        
        
        #line 111 "..\..\..\..\..\Views\ProjectFilesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FileTypeFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\..\..\Views\ProjectFilesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FileSearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\..\..\Views\ProjectFilesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid FilesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 276 "..\..\..\..\..\Views\ProjectFilesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/FinancialTracker;component/views/projectfileswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\ProjectFilesWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ProjectNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 34 "..\..\..\..\..\Views\ProjectFilesWindow.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.AddLetterButton = ((System.Windows.Controls.Button)(target));
            
            #line 38 "..\..\..\..\..\Views\ProjectFilesWindow.xaml"
            this.AddLetterButton.Click += new System.Windows.RoutedEventHandler(this.AddLetterButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.AddFileButton = ((System.Windows.Controls.Button)(target));
            
            #line 40 "..\..\..\..\..\Views\ProjectFilesWindow.xaml"
            this.AddFileButton.Click += new System.Windows.RoutedEventHandler(this.AddFileButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 42 "..\..\..\..\..\Views\ProjectFilesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.TotalFilesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.CommitmentFilesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.InvoiceFilesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.LetterFilesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.TotalSizeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.FileTypeFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 113 "..\..\..\..\..\Views\ProjectFilesWindow.xaml"
            this.FileTypeFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FileTypeFilterComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 12:
            this.FileSearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 128 "..\..\..\..\..\Views\ProjectFilesWindow.xaml"
            this.FileSearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.FileSearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 13:
            this.FilesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 17:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 14:
            
            #line 242 "..\..\..\..\..\Views\ProjectFilesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.OpenFileButton_Click);
            
            #line default
            #line hidden
            break;
            case 15:
            
            #line 249 "..\..\..\..\..\Views\ProjectFilesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CopyPathButton_Click);
            
            #line default
            #line hidden
            break;
            case 16:
            
            #line 256 "..\..\..\..\..\Views\ProjectFilesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteFileButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

